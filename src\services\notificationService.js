import useNotificationStore from "../store/notificationStore";
import { notificationService as apiNotificationService } from "./api";

// أنواع الإشعارات
export const NOTIFICATION_TYPES = {
  BOOKING_CREATED: "booking_created",
  BOOKING_ACCEPTED: "booking_accepted",
  BOOKING_REJECTED: "booking_rejected",
  BOOKING_COMPLETED: "booking_completed",
  BOOKING_CANCELLED: "booking_cancelled",
  REVIEW_RECEIVED: "review_received",
  SYSTEM: "system",
  WELCOME: "welcome",
};

// خدمة الإشعارات
const notificationService = {
  // إنشاء إشعار لطلب جديد
  createBookingNotification: async (booking, userType) => {
    try {
      // تحضير بيانات الإشعار
      let notificationData;

      // إذا كان المستخدم هو الحرفي، نرسل إشعار بوجود طلب جديد
      if (userType === "craftsman") {
        notificationData = {
          type: NOTIFICATION_TYPES.BOOKING_CREATED,
          title: "طلب خدمة جديد",
          message: `لديك طلب خدمة جديد من ${booking.clientName}`,
          data: { bookingId: booking.id },
          icon: "clipboard-list",
        };
      } else {
        // إذا كان المستخدم هو العميل، نرسل إشعار بتأكيد إنشاء الطلب
        notificationData = {
          type: NOTIFICATION_TYPES.BOOKING_CREATED,
          title: "تم إرسال طلب الحجز بنجاح",
          message: `تم إرسال طلب الحجز بنجاح إلى ${booking.craftsmanName}. سيتم إشعارك عندما يقوم الحرفي بالرد على طلبك.`,
          data: { bookingId: booking.id },
          icon: "clipboard-check",
        };
      }

      // إرسال الإشعار إلى الخادم أولاً
      try {
        const serverNotification = await apiNotificationService.createNotification(
          notificationData
        );
        console.log("تم إرسال الإشعار إلى الخادم بنجاح:", serverNotification);

        // إضافة الإشعار محلياً بعد نجاح الإرسال
        const localNotification = useNotificationStore
          .getState()
          .addNotification({
            ...notificationData,
            id: serverNotification.id || Date.now(),
            createdAt: new Date().toISOString(),
            read: false,
          });

        return localNotification;
      } catch (serverError) {
        console.error("فشل إرسال الإشعار إلى الخادم:", serverError);

        // في حالة فشل الخادم، نضيف الإشعار محلياً فقط
        const localNotification = useNotificationStore
          .getState()
          .addNotification({
            ...notificationData,
            id: Date.now(),
            createdAt: new Date().toISOString(),
            read: false,
          });

        return localNotification;
      }
    } catch (error) {
      console.error("خطأ في إنشاء إشعار الطلب:", error);
      // في حالة الفشل، نستخدم المخزن المحلي
      return useNotificationStore.getState().addNotification({
        type:
          userType === "craftsman"
            ? NOTIFICATION_TYPES.BOOKING_CREATED
            : NOTIFICATION_TYPES.BOOKING_CREATED,
        title:
          userType === "craftsman"
            ? "طلب خدمة جديد"
            : "تم إرسال طلب الحجز بنجاح",
        message:
          userType === "craftsman"
            ? `لديك طلب خدمة جديد من ${booking.clientName}`
            : `تم إرسال طلب الحجز بنجاح إلى ${booking.craftsmanName}. سيتم إشعارك عندما يقوم الحرفي بالرد على طلبك.`,
        data: { bookingId: booking.id },
        icon: userType === "craftsman" ? "clipboard-list" : "clipboard-check",
      });
    }
  },

  // إنشاء إشعار لتغيير حالة الطلب
  createStatusChangeNotification: async (booking, status, userType) => {
    try {
      // تجاهل الإشعار إذا كان المستخدم هو من قام بتغيير الحالة
      if (
        (userType === "craftsman" && status === "accepted") ||
        (userType === "client" && status === "cancelled")
      ) {
        return null;
      }

      let notificationType, title, message, icon;

      switch (status) {
        case "accepted":
          notificationType = NOTIFICATION_TYPES.BOOKING_ACCEPTED;
          title = "تم قبول طلب الخدمة";
          message = `تم قبول طلب الخدمة من قبل ${booking.craftsmanName}`;
          icon = "check-circle";
          break;
        case "rejected":
          notificationType = NOTIFICATION_TYPES.BOOKING_REJECTED;
          title = "تم رفض طلب الخدمة";
          message = `تم رفض طلب الخدمة من قبل ${booking.craftsmanName}`;
          icon = "x-circle";
          break;
        case "completed":
          notificationType = NOTIFICATION_TYPES.BOOKING_COMPLETED;
          title = "تم إكمال الخدمة";
          message =
            userType === "client"
              ? `تم إكمال الخدمة من قبل ${booking.craftsmanName}`
              : `تم تأكيد إكمال الخدمة لـ ${booking.clientName}`;
          icon = "check-square";
          break;
        case "cancelled":
          notificationType = NOTIFICATION_TYPES.BOOKING_CANCELLED;
          title = "تم إلغاء طلب الخدمة";
          message =
            userType === "craftsman"
              ? `تم إلغاء طلب الخدمة من قبل ${booking.clientName}`
              : "تم إلغاء طلب الخدمة";
          icon = "x-square";
          break;
        default:
          return null;
      }

      // إعداد بيانات الإشعار
      const notificationData = {
        type: notificationType,
        title,
        message,
        data: { bookingId: booking.id },
        icon,
        createdAt: new Date().toISOString(),
        read: false,
      };

      // إضافة الإشعار محلياً أولاً لضمان ظهوره حتى في حالة فشل الاتصال بالخادم
      const localNotification = useNotificationStore
        .getState()
        .addNotification(notificationData);
      console.log("تم إضافة إشعار تغيير الحالة محلياً:", localNotification);

      // ملاحظة: الإشعار سيتم إنشاؤه في الخادم تلقائياً عند تحديث حالة الطلب
      // لذلك لا نحتاج لإرساله يدوياً هنا
      console.log("تم إضافة الإشعار محلياً، سيتم مزامنته مع الخادم تلقائياً");

      return localNotification;
    } catch (error) {
      console.error("خطأ في إنشاء إشعار تغيير الحالة:", error);
      // في حالة الفشل، نستخدم المخزن المحلي
      return useNotificationStore.getState().addNotification({
        type: getNotificationTypeForStatus(status),
        title: getNotificationTitleForStatus(status),
        message: getNotificationMessageForStatus(status, booking, userType),
        data: { bookingId: booking.id },
        icon: getNotificationIconForStatus(status),
        createdAt: new Date().toISOString(),
        read: false,
      });
    }
  },

  // إنشاء إشعار لتقييم جديد
  createReviewNotification: async (review, booking) => {
    try {
      const notificationData = {
        type: NOTIFICATION_TYPES.REVIEW_RECEIVED,
        title: "تقييم جديد",
        message: `تلقيت تقييماً جديداً (${review.rating ||
          review.overallRating}/5) من ${booking.clientName}`,
        data: { bookingId: booking.id, reviewId: review.id },
        icon: "star",
      };

      // إضافة الإشعار محلياً أولاً لضمان ظهوره حتى في حالة فشل الاتصال بالخادم
      const localNotification = useNotificationStore
        .getState()
        .addNotification(notificationData);
      console.log("تم إضافة إشعار التقييم محلياً:", localNotification);

      // محاولة إرسال الإشعار إلى الخادم مع إعادة المحاولة
      const maxRetries = 3;
      let retryCount = 0;
      let success = false;

      while (retryCount < maxRetries && !success) {
        try {
          const serverNotification = await apiNotificationService.createNotification(
            notificationData
          );
          console.log(
            `تم إرسال إشعار التقييم إلى الخادم (محاولة ${retryCount + 1}):`,
            serverNotification
          );
          success = true;
        } catch (serverError) {
          retryCount++;
          console.error(
            `فشل إرسال إشعار التقييم إلى الخادم (محاولة ${retryCount}/${maxRetries}):`,
            serverError
          );

          if (retryCount < maxRetries) {
            // انتظار قبل إعادة المحاولة (1 ثانية، 2 ثانية، 4 ثانية...)
            const delay = Math.pow(2, retryCount) * 1000;
            console.log(`إعادة المحاولة بعد ${delay / 1000} ثانية...`);
            await new Promise((resolve) => setTimeout(resolve, delay));
          }
        }
      }

      if (!success) {
        console.error(
          "فشلت جميع محاولات إرسال إشعار التقييم إلى الخادم، ولكن تم إضافته محلياً"
        );
      }

      return localNotification;
    } catch (error) {
      console.error("خطأ في إنشاء إشعار التقييم:", error);
      // في حالة الفشل، نستخدم المخزن المحلي
      return useNotificationStore.getState().addNotification({
        type: NOTIFICATION_TYPES.REVIEW_RECEIVED,
        title: "تقييم جديد",
        message: `تلقيت تقييماً جديداً (${review.rating ||
          review.overallRating}/5) من ${booking.clientName}`,
        data: { bookingId: booking.id, reviewId: review.id },
        icon: "star",
      });
    }
  },

  // إنشاء إشعار نظام
  createSystemNotification: async (title, message) => {
    try {
      // إعداد بيانات الإشعار
      const notificationData = {
        type: NOTIFICATION_TYPES.SYSTEM,
        title,
        message,
        icon: "bell",
        createdAt: new Date().toISOString(),
        read: false,
      };

      // إضافة الإشعار محلياً أولاً لضمان ظهوره حتى في حالة فشل الاتصال بالخادم
      const localNotification = useNotificationStore
        .getState()
        .addNotification(notificationData);
      console.log("تم إضافة إشعار النظام محلياً:", localNotification);

      // محاولة إرسال الإشعار إلى الخادم مع إعادة المحاولة
      const maxRetries = 3;
      let retryCount = 0;
      let success = false;

      while (retryCount < maxRetries && !success) {
        try {
          const serverNotification = await apiNotificationService.createNotification(
            notificationData
          );
          console.log(
            `تم إرسال إشعار النظام إلى الخادم (محاولة ${retryCount + 1}):`,
            serverNotification
          );
          success = true;
        } catch (serverError) {
          retryCount++;
          console.error(
            `فشل إرسال إشعار النظام إلى الخادم (محاولة ${retryCount}/${maxRetries}):`,
            serverError
          );

          // إذا كان الخطأ 401، لا نعيد المحاولة لتجنب تسجيل الخروج التلقائي
          if (serverError.response?.status === 401) {
            console.log("تجاهل خطأ 401 في إنشاء إشعار النظام");
            break; // خروج من حلقة إعادة المحاولة
          }

          if (retryCount < maxRetries) {
            // انتظار قبل إعادة المحاولة (1 ثانية، 2 ثانية، 4 ثانية...)
            const delay = Math.pow(2, retryCount) * 1000;
            console.log(`إعادة المحاولة بعد ${delay / 1000} ثانية...`);
            await new Promise((resolve) => setTimeout(resolve, delay));
          }
        }
      }

      if (!success) {
        console.error(
          "فشلت جميع محاولات إرسال إشعار النظام إلى الخادم، ولكن تم إضافته محلياً"
        );
      }

      return localNotification;
    } catch (error) {
      console.error("خطأ في إنشاء إشعار النظام:", error);
      // في حالة الفشل، نستخدم المخزن المحلي
      return useNotificationStore.getState().addNotification({
        type: NOTIFICATION_TYPES.SYSTEM,
        title,
        message,
        icon: "bell",
        createdAt: new Date().toISOString(),
        read: false,
      });
    }
  },

  // إنشاء إشعار ترحيب للمستخدمين الجدد
  createWelcomeNotification: async (userName) => {
    try {
      // إعداد بيانات الإشعار
      const notificationData = {
        type: NOTIFICATION_TYPES.WELCOME,
        title: "مرحباً بك في JobScope!",
        message: `أهلاً وسهلاً ${userName}! نحن سعداء لانضمامك إلى منصة JobScope. استكشف الخدمات المتاحة وابدأ رحلتك معنا.`,
        icon: "heart",
        createdAt: new Date().toISOString(),
        read: false,
      };

      // إضافة الإشعار محلياً أولاً لضمان ظهوره حتى في حالة فشل الاتصال بالخادم
      const localNotification = useNotificationStore
        .getState()
        .addNotification(notificationData);
      console.log("تم إضافة إشعار الترحيب محلياً:", localNotification);

      // محاولة إرسال الإشعار إلى الخادم مع إعادة المحاولة
      const maxRetries = 3;
      let retryCount = 0;
      let success = false;

      while (retryCount < maxRetries && !success) {
        try {
          const serverNotification = await apiNotificationService.createNotification(
            notificationData
          );
          console.log(
            `تم إرسال إشعار الترحيب إلى الخادم (محاولة ${retryCount + 1}):`,
            serverNotification
          );
          success = true;
        } catch (serverError) {
          retryCount++;
          console.error(
            `فشل إرسال إشعار الترحيب إلى الخادم (محاولة ${retryCount}/${maxRetries}):`,
            serverError
          );

          // إذا كان الخطأ 401، لا نعيد المحاولة لتجنب تسجيل الخروج التلقائي
          if (serverError.response?.status === 401) {
            console.log("تجاهل خطأ 401 في إنشاء إشعار الترحيب");
            break; // خروج من حلقة إعادة المحاولة
          }

          if (retryCount < maxRetries) {
            // انتظار قبل إعادة المحاولة (1 ثانية، 2 ثانية، 4 ثانية...)
            const delay = Math.pow(2, retryCount) * 1000;
            console.log(`إعادة المحاولة بعد ${delay / 1000} ثانية...`);
            await new Promise((resolve) => setTimeout(resolve, delay));
          }
        }
      }

      if (!success) {
        console.error(
          "فشلت جميع محاولات إرسال إشعار الترحيب إلى الخادم، ولكن تم إضافته محلياً"
        );
      }

      return localNotification;
    } catch (error) {
      console.error("خطأ في إنشاء إشعار الترحيب:", error);
      // في حالة الفشل، نستخدم المخزن المحلي
      return useNotificationStore.getState().addNotification({
        type: NOTIFICATION_TYPES.WELCOME,
        title: "مرحباً بك في JobScope!",
        message: `أهلاً وسهلاً ${userName}! نحن سعداء لانضمامك إلى منصة JobScope. استكشف الخدمات المتاحة وابدأ رحلتك معنا.`,
        icon: "heart",
        createdAt: new Date().toISOString(),
        read: false,
      });
    }
  },
};

// دوال مساعدة
function getNotificationTypeForStatus(status) {
  switch (status) {
    case "accepted":
      return NOTIFICATION_TYPES.BOOKING_ACCEPTED;
    case "rejected":
      return NOTIFICATION_TYPES.BOOKING_REJECTED;
    case "completed":
      return NOTIFICATION_TYPES.BOOKING_COMPLETED;
    case "cancelled":
    case "cancelled_expired":
      return NOTIFICATION_TYPES.BOOKING_CANCELLED;
    default:
      return NOTIFICATION_TYPES.SYSTEM;
  }
}

function getNotificationTitleForStatus(status) {
  switch (status) {
    case "accepted":
      return "تم قبول طلب الخدمة";
    case "rejected":
      return "تم رفض طلب الخدمة";
    case "completed":
      return "تم إكمال الخدمة";
    case "cancelled":
      return "تم إلغاء طلب الخدمة";
    case "cancelled_expired":
      return "تم إلغاء طلب الخدمة تلقائياً";
    default:
      return "تحديث حالة الطلب";
  }
}

function getNotificationMessageForStatus(status, booking, userType) {
  switch (status) {
    case "accepted":
      return `تم قبول طلب الخدمة من قبل الحرفي ${booking.craftsmanName}`;
    case "rejected":
      return `رفض الحرفي ${booking.craftsmanName} طلب الخدمة`;
    case "completed":
      return userType === "client"
        ? `تم إكمال الخدمة من قبل الحرفي ${booking.craftsmanName}`
        : `تم تأكيد إكمال الخدمة للعميل ${booking.clientName}`;
    case "cancelled":
      return userType === "craftsman"
        ? `تم إلغاء طلب الخدمة من قبل العميل ${booking.clientName}`
        : "تم إلغاء طلب الخدمة من قبلك";
    case "cancelled_expired":
      return "تم إلغاء طلب الخدمة تلقائياً بسبب انتهاء الوقت المحدد";
    default:
      return `تم تحديث حالة الطلب إلى ${status}`;
  }
}

function getNotificationIconForStatus(status) {
  switch (status) {
    case "accepted":
      return "check-circle";
    case "rejected":
      return "x-circle";
    case "completed":
      return "check-square";
    case "cancelled":
      return "x-square";
    default:
      return "bell";
  }
}

export default notificationService;
