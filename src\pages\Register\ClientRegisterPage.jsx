import React, { useState, useEffect, useRef } from "react";
import { Link, useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import Layout from "../../components/layout/Layout";
import Input from "../../components/common/Input";
import Button from "../../components/common/Button";
import LoadingButton from "../../components/common/LoadingButton";
import CountryCodeSelectButtons from "../../components/common/CountryCodeSelectButtons";
import useUserStore from "../../store/userStore";
import useThemeStore from "../../store/themeStore";
import authService from "../../services/authService";
import toastUtils from "../../utils/toastUtils";

const ClientRegisterPage = () => {
  const [phoneVerification, setPhoneVerification] = useState({
    sent: false,
    sending: false,
    verified: false,
    otp: "",
    verifying: false,
  });

  const [formData, setFormData] = useState({
    name: "",
    phone: "",
    countryCode: "+963",
    password: "",
    confirmPassword: "",
  });

  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [otpAttempts, setOtpAttempts] = useState(0);
  const [hourlyBlock, setHourlyBlock] = useState(0);
  const [resendCooldown, setResendCooldown] = useState(0);
  const [showForgotPassword, setShowForgotPassword] = useState(false);
  const [forgotPasswordData, setForgotPasswordData] = useState({
    phone: "",
    countryCode: "+963",
    otp: "",
    newPassword: "",
    confirmNewPassword: "",
  });
  const [forgotPasswordStep, setForgotPasswordStep] = useState(1); // 1: phone, 2: otp, 3: new password

  const login = useUserStore((state) => state.login);
  const darkMode = useThemeStore((state) => state.darkMode);
  const navigate = useNavigate();
  const recaptchaContainerRef = useRef(null);

  // تحميل الحالة الأولية من localStorage
  const loadInitialState = () => {
    const savedState = localStorage.getItem("otpAttemptsState");
    if (savedState) {
      const parsedState = JSON.parse(savedState);
      const now = Date.now();

      if (now - parsedState.timestamp < 3600000) {
        // ساعة واحدة
        setOtpAttempts(parsedState.attempts);
        const remainingTime = 3600000 - (now - parsedState.timestamp);
        if (parsedState.attempts >= 3) {
          setHourlyBlock(Math.floor(remainingTime / 1000));
        }
      } else {
        localStorage.removeItem("otpAttemptsState");
      }
    }
  };

  useEffect(() => {
    loadInitialState();
  }, []);

  // عداد الحظر لمدة ساعة
  useEffect(() => {
    let timer;
    if (hourlyBlock > 0) {
      timer = setTimeout(() => {
        setHourlyBlock(hourlyBlock - 1);
      }, 1000);
    } else if (hourlyBlock === 0 && otpAttempts >= 3) {
      setOtpAttempts(0);
      localStorage.removeItem("otpAttemptsState");
    }
    return () => clearTimeout(timer);
  }, [hourlyBlock, otpAttempts]);

  // عداد إعادة الإرسال
  useEffect(() => {
    let timer;
    if (resendCooldown > 0) {
      timer = setTimeout(() => {
        setResendCooldown(resendCooldown - 1);
      }, 1000);
    }
    return () => clearTimeout(timer);
  }, [resendCooldown]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: "" }));
    }
  };

  const handleForgotPasswordInputChange = (e) => {
    const { name, value } = e.target;
    setForgotPasswordData((prev) => ({ ...prev, [name]: value }));
  };

  const validateStep1 = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = "الاسم مطلوب";
    }

    if (!formData.phone.trim()) {
      newErrors.phone = "رقم الهاتف مطلوب";
    }

    if (!formData.password) {
      newErrors.password = "كلمة المرور مطلوبة";
    } else if (formData.password.length < 6) {
      newErrors.password = "كلمة المرور يجب أن تكون 6 أحرف على الأقل";
    }

    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = "كلمات المرور غير متطابقة";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const sendPhoneVerification = async () => {
    if (otpAttempts >= 3 && hourlyBlock > 0) {
      toastUtils.showToast("تم تجاوز الحد الأقصى للمحاولات", "error", 3000);
      return;
    }

    setPhoneVerification((prev) => ({ ...prev, sending: true }));

    try {
      let phoneToSend = formData.phone;
      if (
        (formData.countryCode === "+963" || formData.countryCode === "963") &&
        phoneToSend.startsWith("0")
      ) {
        phoneToSend = phoneToSend.substring(1);
      }

      const fullPhoneNumber = formData.countryCode + phoneToSend;

      // إرسال OTP عبر API
      const otpResponse = await authService.sendOtpToPhone(fullPhoneNumber);
      if (otpResponse.success) {
        toastUtils.showToast(
          "تم إرسال رمز التحقق عبر WhatsApp",
          "success",
          3000
        );

        setPhoneVerification((prev) => ({
          ...prev,
          sent: true,
          sending: false,
          verified: false, // يجب التحقق من الرمز أولاً
        }));
      } else {
        throw new Error("فشل في إرسال رمز التحقق");
      }

      setResendCooldown(60);
    } catch (error) {
      console.error("Error sending phone verification:", error);
      toastUtils.showToast("فشل في إرسال رمز التحقق", "error", 3000);
      setPhoneVerification((prev) => ({ ...prev, sending: false }));

      const newAttempts = otpAttempts + 1;
      setOtpAttempts(newAttempts);

      if (newAttempts >= 3) {
        setHourlyBlock(3600);
        localStorage.setItem(
          "otpAttemptsState",
          JSON.stringify({
            attempts: newAttempts,
            timestamp: Date.now(),
          })
        );
      }
    }
  };

  // التحقق من رمز الهاتف
  const verifyPhoneCode = async () => {
    if (!phoneVerification.otp || phoneVerification.otp.length !== 6) {
      toastUtils.showToast(
        "يرجى إدخال رمز التحقق المكون من 6 أرقام",
        "error",
        3000
      );
      return;
    }

    setPhoneVerification((prev) => ({ ...prev, verifying: true }));

    try {
      let phoneToSend = formData.phone;
      if (
        (formData.countryCode === "+963" || formData.countryCode === "963") &&
        phoneToSend.startsWith("0")
      ) {
        phoneToSend = phoneToSend.substring(1);
      }
      const fullPhoneNumber = formData.countryCode + phoneToSend;

      const verifyResponse = await authService.verifyOtp(
        fullPhoneNumber,
        phoneVerification.otp
      );

      if (verifyResponse.success) {
        setPhoneVerification((prev) => ({
          ...prev,
          verified: true,
          verifying: false,
        }));
        toastUtils.showToast("تم التحقق من رقم الهاتف بنجاح", "success", 3000);
      } else {
        throw new Error("رمز التحقق غير صحيح");
      }
    } catch (error) {
      console.error("Error verifying phone OTP:", error);
      setPhoneVerification((prev) => ({ ...prev, verifying: false }));
      toastUtils.showToast("رمز التحقق غير صحيح", "error", 3000);
    }
  };

  const handleRegister = async (e) => {
    e.preventDefault();

    if (!validateStep1()) return;

    if (!phoneVerification.verified) {
      toastUtils.showToast("يرجى تأكيد رقم الهاتف قبل المتابعة", "error", 3000);
      return;
    }

    setIsLoading(true);

    try {
      const cleanedPhone = formData.phone.replace(/\D/g, "").replace(/^0+/, "");
      const fullPhoneNumber = formData.countryCode + cleanedPhone;

      // التحقق من عدم وجود حساب بنفس رقم الهاتف
      const phoneCheckResponse = await authService.checkPhoneExists(
        fullPhoneNumber
      );
      if (phoneCheckResponse.exists) {
        setErrors({ phone: "رقم الهاتف مستخدم بالفعل" });
        setIsLoading(false);
        return;
      }

      const userData = {
        name: formData.name,
        phone: fullPhoneNumber,
        email: `${fullPhoneNumber.replace("+", "")}@phone.com`,
        password: formData.password,
        userType: "client",
      };

      const response = await authService.register(userData);
      login(response.user || userData, "client");
      navigate("/home");
    } catch (error) {
      console.error("Registration error:", error);
      toastUtils.showToast(
        error.message || "حدث خطأ أثناء التسجيل",
        "error",
        3000
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleForgotPasswordSubmit = async (e) => {
    e.preventDefault();

    if (forgotPasswordStep === 1) {
      // إرسال OTP لرقم الهاتف
      try {
        const cleanedPhone = forgotPasswordData.phone
          .replace(/\D/g, "")
          .replace(/^0+/, "");
        const fullPhoneNumber = forgotPasswordData.countryCode + cleanedPhone;

        // التحقق من وجود الحساب
        const phoneCheckResponse = await authService.checkPhoneExists(
          fullPhoneNumber
        );
        if (!phoneCheckResponse.exists) {
          toastUtils.showToast("رقم الهاتف غير مسجل", "error", 3000);
          return;
        }

        // إرسال OTP عبر API
        const otpResponse = await authService.sendOtpToPhone(fullPhoneNumber);
        if (otpResponse.success) {
          toastUtils.showToast(
            "تم إرسال رمز التحقق عبر WhatsApp",
            "success",
            3000
          );
        } else {
          toastUtils.showToast("فشل في إرسال رمز التحقق", "error", 3000);
          return;
        }
        setForgotPasswordStep(2);
      } catch (error) {
        toastUtils.showToast("حدث خطأ أثناء إرسال رمز التحقق", "error", 3000);
      }
    } else if (forgotPasswordStep === 2) {
      // التحقق من OTP
      try {
        const cleanedPhone = forgotPasswordData.phone
          .replace(/\D/g, "")
          .replace(/^0+/, "");
        const fullPhoneNumber = forgotPasswordData.countryCode + cleanedPhone;

        const verifyResponse = await authService.verifyOtp(
          fullPhoneNumber,
          forgotPasswordData.otp
        );
        if (verifyResponse.success) {
          setForgotPasswordStep(3);
        } else {
          toastUtils.showToast("رمز التحقق غير صحيح", "error", 3000);
        }
      } catch (error) {
        toastUtils.showToast("رمز التحقق غير صحيح", "error", 3000);
      }
    } else if (forgotPasswordStep === 3) {
      // تغيير كلمة المرور
      if (
        forgotPasswordData.newPassword !== forgotPasswordData.confirmNewPassword
      ) {
        toastUtils.showToast("كلمات المرور غير متطابقة", "error", 3000);
        return;
      }

      try {
        const cleanedPhone = forgotPasswordData.phone
          .replace(/\D/g, "")
          .replace(/^0+/, "");
        const fullPhoneNumber = forgotPasswordData.countryCode + cleanedPhone;

        await authService.resetPassword(
          fullPhoneNumber,
          forgotPasswordData.newPassword
        );
        toastUtils.showToast("تم تغيير كلمة المرور بنجاح", "success", 3000);
        setShowForgotPassword(false);
        setForgotPasswordStep(1);
        setForgotPasswordData({
          phone: "",
          countryCode: "+963",
          otp: "",
          newPassword: "",
          confirmNewPassword: "",
        });
      } catch (error) {
        toastUtils.showToast("حدث خطأ أثناء تغيير كلمة المرور", "error", 3000);
      }
    }
  };

  if (showForgotPassword) {
    return (
      <Layout hideFooter>
        <div
          className={`min-h-screen py-12 ${
            darkMode
              ? "bg-gray-900"
              : "bg-gradient-to-br from-blue-50 to-indigo-100"
          } transition-colors duration-300`}
        >
          <div className="container mx-auto px-4">
            <motion.div
              className={`max-w-lg mx-auto rounded-lg shadow-xl overflow-hidden border-2 ${
                darkMode
                  ? "bg-gray-800 border-gray-700"
                  : "bg-white border-indigo-200"
              } transition-colors duration-300`}
              initial={{ opacity: 0, y: 20, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ duration: 0.5, ease: "easeOut" }}
            >
              <div className="p-6">
                <h2
                  className={`text-2xl font-bold text-center mb-4 ${
                    darkMode ? "text-indigo-300" : "text-indigo-800"
                  } relative transition-colors duration-300`}
                >
                  <span className="relative z-10">استعادة كلمة المرور</span>
                  <span
                    className={`absolute bottom-0 left-0 right-0 h-3 ${
                      darkMode ? "bg-indigo-500" : "bg-indigo-300"
                    } opacity-40 transform -rotate-1 z-0`}
                  ></span>
                </h2>

                <form onSubmit={handleForgotPasswordSubmit}>
                  {forgotPasswordStep === 1 && (
                    <>
                      <div className="mb-4">
                        <label
                          className={`block font-medium mb-2 ${
                            darkMode ? "text-gray-200" : "text-indigo-700"
                          }`}
                        >
                          رقم الهاتف
                        </label>
                        <div className="grid grid-cols-3 gap-2">
                          <div className="col-span-2">
                            <input
                              type="tel"
                              name="phone"
                              value={forgotPasswordData.phone}
                              onChange={handleForgotPasswordInputChange}
                              placeholder="أدخل رقم الهاتف"
                              required
                              className="w-full py-2 px-3 border border-indigo-200 rounded-md focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                              dir="ltr"
                            />
                          </div>
                          <div>
                            <CountryCodeSelectButtons
                              value={forgotPasswordData.countryCode}
                              onChange={(code) =>
                                setForgotPasswordData((prev) => ({
                                  ...prev,
                                  countryCode: code,
                                }))
                              }
                            />
                          </div>
                        </div>
                      </div>
                    </>
                  )}

                  {forgotPasswordStep === 2 && (
                    <div className="mb-4">
                      <label
                        className={`block font-medium mb-2 ${
                          darkMode ? "text-gray-200" : "text-indigo-700"
                        }`}
                      >
                        رمز التحقق
                      </label>
                      <input
                        type="text"
                        name="otp"
                        value={forgotPasswordData.otp}
                        onChange={handleForgotPasswordInputChange}
                        placeholder="أدخل رمز التحقق"
                        required
                        className="w-full py-2 px-3 border border-indigo-200 rounded-md focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                        dir="ltr"
                      />
                    </div>
                  )}

                  {forgotPasswordStep === 3 && (
                    <>
                      <Input
                        label="كلمة المرور الجديدة"
                        type="password"
                        name="newPassword"
                        value={forgotPasswordData.newPassword}
                        onChange={handleForgotPasswordInputChange}
                        placeholder="أدخل كلمة المرور الجديدة"
                        required
                        className="mb-3"
                      />
                      <Input
                        label="تأكيد كلمة المرور الجديدة"
                        type="password"
                        name="confirmNewPassword"
                        value={forgotPasswordData.confirmNewPassword}
                        onChange={handleForgotPasswordInputChange}
                        placeholder="أعد إدخال كلمة المرور الجديدة"
                        required
                        className="mb-3"
                      />
                    </>
                  )}

                  <LoadingButton
                    type="submit"
                    variant="primary"
                    fullWidth
                    className="mt-4 bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600"
                  >
                    {forgotPasswordStep === 1 && "إرسال رمز التحقق"}
                    {forgotPasswordStep === 2 && "تأكيد الرمز"}
                    {forgotPasswordStep === 3 && "تغيير كلمة المرور"}
                  </LoadingButton>

                  <div className="mt-4 text-center">
                    <button
                      type="button"
                      onClick={() => {
                        setShowForgotPassword(false);
                        setForgotPasswordStep(1);
                      }}
                      className={`text-sm ${
                        darkMode ? "text-indigo-400" : "text-indigo-600"
                      } hover:underline`}
                    >
                      العودة إلى التسجيل
                    </button>
                  </div>
                </form>
              </div>
            </motion.div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout hideFooter>
      <div
        className={`min-h-screen py-12 ${
          darkMode
            ? "bg-gray-900"
            : "bg-gradient-to-br from-blue-50 to-indigo-100"
        } transition-colors duration-300`}
      >
        <div className="container mx-auto px-4">
          <motion.div
            className={`max-w-lg mx-auto rounded-lg shadow-xl overflow-hidden border-2 ${
              darkMode
                ? "bg-gray-800 border-gray-700"
                : "bg-white border-indigo-200"
            } transition-colors duration-300`}
            initial={{ opacity: 0, y: 20, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ duration: 0.5, ease: "easeOut" }}
          >
            <div className="p-6">
              <h2
                className={`text-2xl font-bold text-center mb-4 ${
                  darkMode ? "text-indigo-300" : "text-indigo-800"
                } relative transition-colors duration-300`}
              >
                <span className="relative z-10">إنشاء حساب جديد</span>
                <span
                  className={`absolute bottom-0 left-0 right-0 h-3 ${
                    darkMode ? "bg-indigo-500" : "bg-indigo-300"
                  } opacity-40 transform -rotate-1 z-0`}
                ></span>
              </h2>

              <div className="mb-4 text-center">
                <p
                  className={`${
                    darkMode ? "text-gray-300" : "text-gray-600"
                  } text-sm`}
                >
                  سجل كطالب خدمة للوصول إلى أفضل الحرفيين في منطقتك
                </p>
              </div>

              <form onSubmit={handleRegister}>
                <Input
                  label="الاسم الكامل"
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder="أدخل اسمك الكامل"
                  error={errors.name}
                  required
                  className="mb-3"
                />

                <div className="mb-3">
                  <label
                    className={`block font-medium mb-2 ${
                      darkMode ? "text-gray-200" : "text-indigo-700"
                    }`}
                  >
                    رقم الهاتف
                  </label>
                  <div className="grid grid-cols-3 gap-2">
                    <div className="col-span-2">
                      <input
                        type="tel"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        placeholder="أدخل رقم الهاتف"
                        required
                        className="w-full py-2 px-3 border border-indigo-200 rounded-md focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                        dir="ltr"
                      />
                    </div>
                    <div>
                      <CountryCodeSelectButtons
                        value={formData.countryCode}
                        onChange={(code) =>
                          setFormData((prev) => ({
                            ...prev,
                            countryCode: code,
                          }))
                        }
                        disabled={isLoading}
                      />
                    </div>
                  </div>
                  {errors.phone && (
                    <p className="mt-1 text-sm text-red-600">{errors.phone}</p>
                  )}

                  <div className="mt-2">
                    <Button
                      type="button"
                      variant="secondary"
                      disabled={
                        phoneVerification.sending ||
                        !formData.phone ||
                        resendCooldown > 0 ||
                        phoneVerification.verified
                      }
                      onClick={sendPhoneVerification}
                      className="w-full"
                    >
                      {phoneVerification.verified
                        ? "✓ تم التحقق"
                        : phoneVerification.sending
                        ? "جاري الإرسال..."
                        : resendCooldown > 0
                        ? `إعادة الإرسال (${resendCooldown})`
                        : "إرسال رمز التحقق"}
                    </Button>
                  </div>

                  {/* إدخال رمز التحقق للهاتف */}
                  {phoneVerification.sent && !phoneVerification.verified && (
                    <div className="mt-3">
                      <label
                        className={`block font-medium mb-2 ${
                          darkMode ? "text-gray-200" : "text-indigo-700"
                        }`}
                      >
                        رمز التحقق
                      </label>
                      <div className="flex items-center gap-2">
                        <div className="flex-grow">
                          <input
                            type="text"
                            placeholder="أدخل رمز التحقق (6 أرقام)"
                            value={phoneVerification.otp}
                            onChange={(e) => {
                              const value = e.target.value
                                .replace(/\D/g, "")
                                .slice(0, 6);
                              setPhoneVerification((prev) => ({
                                ...prev,
                                otp: value,
                              }));
                            }}
                            className="w-full py-2 px-3 border border-indigo-200 rounded-md focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                            dir="ltr"
                            maxLength={6}
                          />
                        </div>
                        <Button
                          type="button"
                          variant="primary"
                          disabled={
                            phoneVerification.verifying ||
                            phoneVerification.otp.length !== 6
                          }
                          onClick={verifyPhoneCode}
                          className="bg-green-500 hover:bg-green-600"
                        >
                          {phoneVerification.verifying
                            ? "جاري التحقق..."
                            : "تحقق"}
                        </Button>
                      </div>
                    </div>
                  )}
                </div>

                <Input
                  label="كلمة المرور"
                  type="password"
                  name="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  placeholder="أدخل كلمة المرور"
                  error={errors.password}
                  required
                  className="mb-3"
                />

                <Input
                  label="تأكيد كلمة المرور"
                  type="password"
                  name="confirmPassword"
                  value={formData.confirmPassword}
                  onChange={handleInputChange}
                  placeholder="أعد إدخال كلمة المرور"
                  error={errors.confirmPassword}
                  required
                  className="mb-3"
                />

                <LoadingButton
                  type="submit"
                  variant="primary"
                  fullWidth
                  isLoading={isLoading}
                  loadingText="جاري التسجيل..."
                  className="mt-4 bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600"
                >
                  إنشاء الحساب
                </LoadingButton>

                <div className="mt-4 text-center">
                  <button
                    type="button"
                    onClick={() => setShowForgotPassword(true)}
                    className={`text-sm ${
                      darkMode ? "text-indigo-400" : "text-indigo-600"
                    } hover:underline`}
                  >
                    نسيت كلمة المرور؟
                  </button>
                </div>
              </form>

              <div className="mt-6 text-center">
                <p
                  className={`${
                    darkMode ? "text-gray-300" : "text-indigo-600"
                  } text-sm mb-3`}
                >
                  هل تريد التسجيل كحرفي بدلاً من ذلك؟{" "}
                  <Link
                    to="/register/craftsman"
                    className={`${
                      darkMode ? "text-indigo-400" : "text-indigo-600"
                    } font-medium hover:text-indigo-800 transition-colors duration-200`}
                  >
                    انتقل إلى تسجيل حرفي
                  </Link>
                </p>
              </div>

              <div className="mt-4 text-center">
                <Link
                  to="/"
                  className="text-indigo-500 text-sm hover:text-indigo-700 transition-colors duration-200"
                >
                  العودة إلى الصفحة الرئيسية
                </Link>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </Layout>
  );
};

export default ClientRegisterPage;
