import React from "react";
import { motion } from "framer-motion";
import useThemeStore from "../../../store/themeStore";
import {
  Calendar,
  Search,
  MessageSquare,
  Star,
  UserCheck,
  CheckCircle,
} from "lucide-react";

const HowItWorksSection = () => {
  const darkMode = useThemeStore((state) => state.darkMode);

  // تكوين التأثيرات الحركية
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
      },
    },
  };

  return (
    <section
      id="how-it-works"
      className={`py-24 ${
        darkMode
          ? "bg-gradient-to-b from-gray-900 via-indigo-950/30 to-gray-900"
          : "bg-gradient-to-b from-blue-50 via-indigo-100/50 to-blue-50"
      } transition-colors duration-300 relative overflow-hidden section-title`}
    >
      {/* زخارف الخلفية */}
      <div className="absolute inset-0 overflow-hidden bg-pattern opacity-30"></div>

      {/* خط متصل بين الخطوات */}
      <div className="absolute top-1/2 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-indigo-500/30 to-transparent hidden md:block"></div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <h2
            className={`text-3xl md:text-5xl font-bold ${
              darkMode ? "text-indigo-300" : "text-indigo-800"
            } relative inline-block transition-colors duration-300 mb-4`}
          >
            <span className="relative z-10">كيف تعمل المنصة؟</span>
            <span
              className={`absolute bottom-0 left-0 right-0 h-3 ${
                darkMode ? "bg-indigo-500" : "bg-indigo-300"
              } opacity-40 transform -rotate-1 z-0`}
            ></span>
          </h2>

          <p
            className={`max-w-3xl mx-auto text-lg ${
              darkMode ? "text-gray-300" : "text-gray-700"
            }`}
          >
            منصة JobScope توفر تجربة سلسة وبسيطة لربط طالبي الخدمات بالحرفيين
            المهرة في سوريا
          </p>
        </motion.div>

        <motion.div
          className="grid grid-cols-1 md:grid-cols-3 gap-8 md:gap-6 lg:gap-10 items-stretch"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {/* الخطوة الأولى */}
          <motion.div
            className={`p-8 rounded-2xl shadow-xl border relative z-10 feature-card h-full flex flex-col ${
              darkMode
                ? "bg-gradient-to-br from-indigo-900/90 to-indigo-800/90 text-gray-200 border-indigo-700"
                : "bg-gradient-to-br from-white to-blue-50/90 border-blue-200"
            }`}
            variants={itemVariants}
          >
            {/* رقم الخطوة مع أيقونة */}
            <div className="flex items-center justify-center mb-6">
              <div className="w-20 h-20 bg-gradient-to-br from-indigo-600 to-indigo-800 text-white rounded-full flex items-center justify-center text-2xl font-bold shadow-lg relative">
                <span className="absolute inset-0 rounded-full bg-indigo-600 animate-ping opacity-20"></span>
                <UserCheck size={32} />
              </div>
            </div>

            {/* محتوى الخطوة */}
            <div className="text-center flex-grow flex flex-col justify-between">
              <div className="flex items-center justify-center gap-2 mb-2">
                <span
                  className={`inline-block px-3 py-1 rounded-full text-sm font-bold ${
                    darkMode
                      ? "bg-indigo-700 text-white"
                      : "bg-indigo-100 text-indigo-800"
                  }`}
                >
                  الخطوة 1
                </span>
              </div>

              <h3
                className={`text-2xl font-bold mb-3 ${
                  darkMode ? "text-white" : "text-indigo-800"
                }`}
              >
                سجل حسابك
              </h3>

              <div
                className={`w-16 h-1 mx-auto mb-4 rounded ${
                  darkMode ? "bg-indigo-500" : "bg-indigo-400"
                }`}
              ></div>

              <p
                className={`${
                  darkMode ? "text-indigo-200" : "text-indigo-700"
                } mb-4 text-lg`}
              >
                سجل كحرفي لعرض خدماتك أو استكشف المنصة كطالب خدمة
              </p>

              <ul
                className={`text-right ${
                  darkMode ? "text-indigo-300" : "text-indigo-600"
                } space-y-2`}
              >
                <li className="flex items-center gap-2">
                  <CheckCircle size={16} className="flex-shrink-0" />
                  <span>إنشاء حساب بسهولة باستخدام البريد الإلكتروني</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle size={16} className="flex-shrink-0" />
                  <span>تخصيص ملفك الشخصي بالمعلومات المناسبة</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle size={16} className="flex-shrink-0" />
                  <span>تحديد مهاراتك ومجالات خبرتك</span>
                </li>
              </ul>
            </div>
          </motion.div>

          {/* الخطوة الثانية */}
          <motion.div
            className={`p-8 rounded-2xl shadow-xl border relative z-10 feature-card h-full flex flex-col ${
              darkMode
                ? "bg-gradient-to-br from-blue-900/90 to-indigo-800/90 text-gray-200 border-blue-700"
                : "bg-gradient-to-br from-white to-indigo-100/90 border-indigo-300"
            }`}
            variants={itemVariants}
          >
            {/* رقم الخطوة مع أيقونة */}
            <div className="flex items-center justify-center mb-6">
              <div className="w-20 h-20 bg-gradient-to-br from-blue-600 to-indigo-700 text-white rounded-full flex items-center justify-center text-2xl font-bold shadow-lg relative">
                <span className="absolute inset-0 rounded-full bg-blue-600 animate-ping opacity-20"></span>
                <Search size={32} />
              </div>
            </div>

            {/* محتوى الخطوة */}
            <div className="text-center flex-grow flex flex-col justify-between">
              <div className="flex items-center justify-center gap-2 mb-2">
                <span
                  className={`inline-block px-3 py-1 rounded-full text-sm font-bold ${
                    darkMode
                      ? "bg-blue-700 text-white"
                      : "bg-blue-100 text-blue-800"
                  }`}
                >
                  الخطوة 2
                </span>
              </div>

              <h3
                className={`text-2xl font-bold mb-3 ${
                  darkMode ? "text-white" : "text-indigo-800"
                }`}
              >
                ابحث عن الخدمة
              </h3>

              <div
                className={`w-16 h-1 mx-auto mb-4 rounded ${
                  darkMode ? "bg-blue-500" : "bg-blue-400"
                }`}
              ></div>

              <p
                className={`${
                  darkMode ? "text-indigo-200" : "text-indigo-700"
                } mb-4 text-lg`}
              >
                ابحث عن الحرفي المناسب في منطقتك حسب التخصص والتقييم
              </p>

              <ul
                className={`text-right ${
                  darkMode ? "text-indigo-300" : "text-indigo-600"
                } space-y-2`}
              >
                <li className="flex items-center gap-2">
                  <CheckCircle size={16} className="flex-shrink-0" />
                  <span>استخدام خيارات البحث المتقدمة</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle size={16} className="flex-shrink-0" />
                  <span>تحديد المهنة والموقع ونطاق العمل</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle size={16} className="flex-shrink-0" />
                  <span>تصفية النتائج حسب التقييمات والتوفر</span>
                </li>
              </ul>
            </div>
          </motion.div>

          {/* الخطوة الثالثة */}
          <motion.div
            className={`p-8 rounded-2xl shadow-xl border relative z-10 feature-card h-full flex flex-col ${
              darkMode
                ? "bg-gradient-to-br from-purple-900/90 to-indigo-800/90 text-gray-200 border-purple-700"
                : "bg-gradient-to-br from-white to-blue-100/90 border-blue-300"
            }`}
            variants={itemVariants}
          >
            {/* رقم الخطوة مع أيقونة */}
            <div className="flex items-center justify-center mb-6">
              <div className="w-20 h-20 bg-gradient-to-br from-purple-600 to-indigo-700 text-white rounded-full flex items-center justify-center text-2xl font-bold shadow-lg relative">
                <span className="absolute inset-0 rounded-full bg-purple-600 animate-ping opacity-20"></span>
                <Calendar size={32} />
              </div>
            </div>

            {/* محتوى الخطوة */}
            <div className="text-center flex-grow flex flex-col justify-between">
              <div className="flex items-center justify-center gap-2 mb-2">
                <span
                  className={`inline-block px-3 py-1 rounded-full text-sm font-bold ${
                    darkMode
                      ? "bg-purple-700 text-white"
                      : "bg-purple-100 text-purple-800"
                  }`}
                >
                  الخطوة 3
                </span>
              </div>

              <h3
                className={`text-2xl font-bold mb-3 ${
                  darkMode ? "text-white" : "text-indigo-800"
                }`}
              >
                احجز واستمتع بالخدمة
              </h3>

              <div
                className={`w-16 h-1 mx-auto mb-4 rounded ${
                  darkMode ? "bg-purple-500" : "bg-purple-400"
                }`}
              ></div>

              <p
                className={`${
                  darkMode ? "text-indigo-200" : "text-indigo-700"
                } mb-4 text-lg`}
              >
                تواصل مع الحرفي واحجز موعداً مناسباً وقيّم الخدمة بعد الانتهاء
              </p>

              <ul
                className={`text-right ${
                  darkMode ? "text-indigo-300" : "text-indigo-600"
                } space-y-2`}
              >
                <li className="flex items-center gap-2">
                  <CheckCircle size={16} className="flex-shrink-0" />
                  <span>التواصل المباشر مع الحرفي</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle size={16} className="flex-shrink-0" />
                  <span>تحديد موعد مناسب ومتابعة حالة الطلب</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle size={16} className="flex-shrink-0" />
                  <span>تقييم الخدمة بعد إتمامها</span>
                </li>
              </ul>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default HowItWorksSection;
