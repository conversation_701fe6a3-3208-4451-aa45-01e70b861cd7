import { create } from "zustand";
import { notificationService } from "../services/api";

const useNotificationStore = create((set, get) => ({
  notifications: [],
  unreadCount: 0,
  isLoading: false,
  error: null,

  // تحميل الإشعارات من الخادم
  fetchNotifications: async () => {
    set({ isLoading: true, error: null });
    try {
      // محاولة جلب الإشعارات من الخادم
      try {
        const { notificationService } = await import("../services/api");
        const serverNotifications = await notificationService.getMyNotifications();

        if (serverNotifications && Array.isArray(serverNotifications)) {
          console.log(
            "تم جلب الإشعارات من الخادم:",
            serverNotifications.length
          );

          // حساب عدد الإشعارات غير المقروءة
          const unreadCount = serverNotifications.filter((notif) => !notif.read)
            .length;

          console.log("تحديث الإشعارات من الخادم:", {
            totalNotifications: serverNotifications.length,
            unreadCount,
            notifications: serverNotifications.map((n) => ({
              id: n.id || n._id,
              title: n.title,
              read: n.read,
            })),
          });

          set({
            notifications: serverNotifications,
            unreadCount,
            isLoading: false,
          });
          return;
        }
      } catch (serverError) {
        console.log(
          "فشل جلب الإشعارات من الخادم، استخدام البيانات المحلية:",
          serverError.message
        );

        // إذا كان الخطأ 401، لا نقوم بأي شيء لتجنب تسجيل الخروج التلقائي
        if (serverError.response?.status === 401) {
          console.log("تجاهل خطأ 401 في جلب الإشعارات");
          set({ isLoading: false });
          return;
        }
      }

      // استخدام الإشعارات المخزنة محلياً كبديل
      const notifications = get().notifications || [];
      const unreadCount = notifications.filter((notif) => !notif.read).length;

      set({
        notifications,
        unreadCount,
        isLoading: false,
      });
    } catch (error) {
      console.error("خطأ في تحميل الإشعارات:", error);
      set({
        error: error.message || "حدث خطأ أثناء تحميل الإشعارات",
        isLoading: false,
      });
    }
  },

  // إضافة إشعار جديد
  addNotification: async (notification) => {
    try {
      // إنشاء إشعار محلي
      const localNotification = {
        id: Date.now(),
        _id: Date.now().toString(),
        createdAt: new Date().toISOString(),
        timestamp: new Date().toISOString(),
        read: false,
        ...notification,
      };

      // تحديث القائمة المحلية
      set((state) => ({
        notifications: [localNotification, ...state.notifications],
        unreadCount: state.unreadCount + 1,
      }));

      return localNotification;
    } catch (error) {
      console.error("خطأ في إضافة الإشعار:", error);
      // في حالة الفشل، نضيف الإشعار محليًا فقط
      const localNotification = {
        id: Date.now(),
        _id: Date.now().toString(),
        createdAt: new Date().toISOString(),
        timestamp: new Date().toISOString(),
        read: false,
        ...notification,
      };

      set((state) => ({
        notifications: [localNotification, ...state.notifications],
        unreadCount: state.unreadCount + 1,
      }));

      return localNotification;
    }
  },

  // تحديد إشعار كمقروء
  markAsRead: async (notificationId) => {
    try {
      // التحقق مما إذا كان الإشعار غير مقروء قبل تحديثه
      const notification = get().notifications.find(
        (notif) => notif.id === notificationId || notif._id === notificationId
      );

      if (!notification) {
        console.error("لم يتم العثور على الإشعار:", notificationId);
        return;
      }

      const wasUnread = notification && !notification.read;
      if (!wasUnread) {
        return;
      }

      // تخطي تحديث الإشعار في الخادم

      // تحديث القائمة المحلية
      set((state) => {
        const updatedNotifications = state.notifications.map((notif) =>
          notif.id === notificationId || notif._id === notificationId
            ? { ...notif, read: true }
            : notif
        );

        const newUnreadCount = Math.max(0, state.unreadCount - 1);

        console.log("تحديث الإشعار كمقروء:", {
          notificationId,
          oldUnreadCount: state.unreadCount,
          newUnreadCount,
          wasUnread,
          updatedNotification: updatedNotifications.find(
            (n) => n.id === notificationId || n._id === notificationId
          ),
        });

        return {
          notifications: updatedNotifications,
          unreadCount: newUnreadCount,
        };
      });
    } catch (error) {
      console.error("خطأ في تحديث الإشعار كمقروء:", error);

      // في حالة الفشل، نحدث محليًا فقط
      set((state) => {
        const updatedNotifications = state.notifications.map((notif) =>
          notif.id === notificationId || notif._id === notificationId
            ? { ...notif, read: true }
            : notif
        );

        return {
          notifications: updatedNotifications,
          unreadCount: Math.max(0, state.unreadCount - 1),
        };
      });
    }
  },

  // تحديد جميع الإشعارات كمقروءة
  markAllAsRead: async () => {
    try {
      // تخطي تحديث الإشعارات في الخادم

      // تحديث القائمة المحلية
      set((state) => ({
        notifications: state.notifications.map((notif) => ({
          ...notif,
          read: true,
        })),
        unreadCount: 0,
      }));
    } catch (error) {
      console.error("خطأ في تحديد جميع الإشعارات كمقروءة:", error);

      // في حالة الفشل، نحدث محليًا فقط
      set((state) => ({
        notifications: state.notifications.map((notif) => ({
          ...notif,
          read: true,
        })),
        unreadCount: 0,
      }));
    }
  },

  // حذف إشعار
  deleteNotification: async (notificationId) => {
    try {
      // التحقق مما إذا كان الإشعار غير مقروء قبل حذفه
      const notification = get().notifications.find(
        (notif) => notif.id === notificationId || notif._id === notificationId
      );

      if (!notification) {
        console.error("لم يتم العثور على الإشعار للحذف:", notificationId);
        return;
      }

      const wasUnread = notification && !notification.read;

      // حذف الإشعار من الخادم
      await notificationService.deleteNotification(
        notification._id || notification.id
      );

      // تحديث القائمة المحلية
      set((state) => {
        const updatedNotifications = state.notifications.filter(
          (notif) => notif.id !== notificationId && notif._id !== notificationId
        );

        return {
          notifications: updatedNotifications,
          unreadCount: wasUnread
            ? Math.max(0, state.unreadCount - 1)
            : state.unreadCount,
        };
      });
    } catch (error) {
      console.error("خطأ في حذف الإشعار:", error);

      // في حالة الفشل، نحذف محليًا فقط
      set((state) => {
        const notification = state.notifications.find(
          (notif) => notif.id === notificationId || notif._id === notificationId
        );
        const wasUnread = notification && !notification.read;

        const updatedNotifications = state.notifications.filter(
          (notif) => notif.id !== notificationId && notif._id !== notificationId
        );

        return {
          notifications: updatedNotifications,
          unreadCount: wasUnread
            ? Math.max(0, state.unreadCount - 1)
            : state.unreadCount,
        };
      });
    }
  },

  // حذف جميع الإشعارات
  clearAllNotifications: async () => {
    try {
      // حذف جميع الإشعارات من الخادم
      await notificationService.deleteAllNotifications();

      // تحديث القائمة المحلية
      set({ notifications: [], unreadCount: 0 });
    } catch (error) {
      console.error("خطأ في حذف جميع الإشعارات:", error);

      // في حالة الفشل، نحذف محليًا فقط
      set({ notifications: [], unreadCount: 0 });
    }
  },

  // حذف إشعار الترحيب عند النقر عليه
  deleteWelcomeNotification: async () => {
    try {
      // البحث عن إشعار الترحيب
      const welcomeNotification = get().notifications.find(
        (notif) => notif.type === "welcome"
      );

      if (!welcomeNotification) {
        console.log("لم يتم العثور على إشعار الترحيب");
        return;
      }

      const wasUnread = !welcomeNotification.read;

      // حذف الإشعار من القائمة المحلية
      set((state) => {
        const updatedNotifications = state.notifications.filter(
          (notif) => notif.type !== "welcome"
        );

        const newUnreadCount = wasUnread
          ? Math.max(0, state.unreadCount - 1)
          : state.unreadCount;

        console.log("حذف إشعار الترحيب:", {
          welcomeNotificationId:
            welcomeNotification.id || welcomeNotification._id,
          wasUnread,
          oldUnreadCount: state.unreadCount,
          newUnreadCount,
          remainingNotifications: updatedNotifications.length,
        });

        return {
          notifications: updatedNotifications,
          unreadCount: newUnreadCount,
        };
      });

      // محاولة حذف الإشعار من الخادم (اختياري)
      try {
        if (welcomeNotification._id || welcomeNotification.id) {
          await notificationService.deleteNotification(
            welcomeNotification._id || welcomeNotification.id
          );
        }
      } catch (serverError) {
        console.log("تعذر حذف إشعار الترحيب من الخادم:", serverError.message);
        // لا نعيد الإشعار في حالة فشل الحذف من الخادم
      }

      console.log("تم حذف إشعار الترحيب بنجاح");
    } catch (error) {
      console.error("خطأ في حذف إشعار الترحيب:", error);
    }
  },
}));

export default useNotificationStore;
