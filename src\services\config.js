// ملف التكوين للتطبيق

// عنوان API
// استخدم دائماً عنوان URL المستضاف على Render
export const API_URL = "https://jobscope-8t58.onrender.com/api";

// عنوان الخادم الكامل (للصور وغيرها)
export const SERVER_URL = "https://jobscope-8t58.onrender.com";

// حالات الطلبات
export const BOOKING_STATUS = {
  DRAFT: "draft", // مسودة (قابل للتعديل)
  PENDING: "pending", // قيد الانتظار
  ACCEPTED: "accepted", // تمت الموافقة عليه
  COMPLETED: "completed", // مكتمل
  REJECTED: "rejected", // مرفوض
  CANCELLED: "cancelled", // ملغي
  CANCELLED_EXPIRED: "cancelled_expired", // ملغي بسبب انتهاء الوقت
};

// رسائل حالات الطلبات
export const BOOKING_STATUS_MESSAGES = {
  [BOOKING_STATUS.DRAFT]: "مسودة",
  [BOOKING_STATUS.PENDING]: "قيد الانتظار",
  [BOOKING_STATUS.ACCEPTED]: "مقبول",
  [BOOKING_STATUS.COMPLETED]: "مكتمل",
  [BOOKING_STATUS.REJECTED]: "مرفوض من الحرفي",
  [BOOKING_STATUS.CANCELLED]: "ملغي من العميل",
  [BOOKING_STATUS.CANCELLED_EXPIRED]: "ملغى تلقائياً - انتهى الوقت",
};

// رسائل مفصلة لحالات الطلبات
export const BOOKING_STATUS_DETAILED_MESSAGES = {
  [BOOKING_STATUS.DRAFT]: "هذا الطلب ما زال في مرحلة المسودة",
  [BOOKING_STATUS.PENDING]: "في انتظار رد الحرفي",
  [BOOKING_STATUS.ACCEPTED]: "تم قبول الطلب من الحرفي",
  [BOOKING_STATUS.COMPLETED]: "تم إنجاز العمل بنجاح",
  [BOOKING_STATUS.REJECTED]: "رفض الحرفي هذا الطلب",
  [BOOKING_STATUS.CANCELLED]: "تم إلغاء الطلب من قبل العميل",
  [BOOKING_STATUS.CANCELLED_EXPIRED]:
    "تم إلغاء الطلب تلقائياً لانتهاء الوقت المحدد",
};

// إعدادات الطلبات
export const BOOKING_SETTINGS = {
  // المدة التي يمكن خلالها تعديل الطلب (بالدقائق)
  EDIT_WINDOW_MINUTES: 10,

  // المدة التي يتم بعدها إلغاء الطلبات المعلقة تلقائيًا (بالساعات)
  AUTO_CANCEL_PENDING_HOURS: 24,

  // عدد الطلبات في الصفحة الواحدة
  ITEMS_PER_PAGE: 5,

  // حالة الطلب بعد الإنشاء وقبل إرساله للحرفي
  DRAFT_STATUS: "draft",

  // عتبات تغيير لون الوقت المتبقي (بالدقائق)
  TIME_WARNING_THRESHOLD: 5, // أصفر
  TIME_DANGER_THRESHOLD: 2, // أحمر
};

// إعدادات أخرى
export const APP_CONFIG = {
  // الإعدادات العامة - سيتم تحديثها ديناميكياً من إعدادات الموقع
  appName: "JobScope", // قيمة افتراضية

  // إعدادات الخريطة
  map: {
    defaultCenter: { lat: 33.5138, lng: 36.2765 }, // دمشق
    defaultZoom: 13,
    maxZoom: 18,
    minZoom: 5,
  },

  // إعدادات التحميل
  upload: {
    maxFileSize: 5 * 1024 * 1024, // 5 ميجابايت
    allowedImageTypes: ["image/jpeg", "image/png", "image/webp"],
  },

  // إعدادات المصادقة
  auth: {
    tokenExpiryDays: 30,
    googleClientId:
      import.meta.env.VITE_GOOGLE_CLIENT_ID ||
      "79461320705-ru94lkf71prenrqpv9v9pnvnlvndcseb.apps.googleusercontent.com",
    // إعدادات Supabase (بديل Firebase)
    supabase: {
      url:
        import.meta.env.VITE_SUPABASE_URL ||
        "https://geqnmbnhyzzhqcouldfz.supabase.co",
      anonKey:
        import.meta.env.VITE_SUPABASE_ANON_KEY ||
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdlcW5tYm5oeXp6aHFjb3VsZGZ6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgxOTI3NTMsImV4cCI6MjA2Mzc2ODc1M30.TV92S0BtPGtihgoKjcsW2svZl74_EdcrtJ60AUnIaHw",
    },
  },
};
