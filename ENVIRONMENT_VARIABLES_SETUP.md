# 🔐 إعداد متغيرات البيئة في Render

## 📋 قائمة المتغيرات المطلوبة

### متغيرات الباك إند (Backend)

| اسم المتغير             | الوصف                       | القيمة الحالية                                                                                                                                                                                                     |
| ----------------------- | --------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| `PORT`                  | منفذ الخادم                 | `5000`                                                                                                                                                                                                             |
| `NODE_ENV`              | بيئة التشغيل                | `production`                                                                                                                                                                                                       |
| `MONGODB_URI`           | رابط قاعدة البيانات MongoDB | `mongodb+srv://jobscope_user:<EMAIL>/jobscope?retryWrites=true&w=majority&appName=JobScope`                                                                                     |
| `JWT_SECRET`            | مفتاح تشفير JWT             | `jobscope_secret_key_2024`                                                                                                                                                                                         |
| `EMAIL_SERVICE`         | خدمة البريد الإلكتروني      | `gmail`                                                                                                                                                                                                            |
| `EMAIL_USER`            | بريد المرسل                 | `<EMAIL>`                                                                                                                                                                                             |
| `EMAIL_PASSWORD`        | كلمة مرور التطبيق           | `your-app-password`                                                                                                                                                                                                |
| `EMAIL_FROM`            | اسم المرسل                  | `JobScope <<EMAIL>>`                                                                                                                                                                                  |
| `DEBUG`                 | تفعيل التشخيص               | `false`                                                                                                                                                                                                            |
| `DEBUG_LEVEL`           | مستوى التشخيص               | `info`                                                                                                                                                                                                             |
| `HYPERSENDER_API_TOKEN` | رمز HyperSender API         | `250\|e2Lq3UqTPIzYJBYhdJviP1Zb066RBHuOCWtkj5eY90306903`                                                                                                                                                            |
| `HYPERSENDER_SENDER_ID` | معرف المرسل                 | `JobScope`                                                                                                                                                                                                         |
| `HYPERSENDER_API_URL`   | رابط HyperSender API        | `https://api.hypersender.com/api/send`                                                                                                                                                                             |
| `IMGBB_API_KEY`         | مفتاح Imgbb لرفع الصور      | `your_imgbb_api_key_here`                                                                                                                                                                                          |
| `GEOAPIFY_API_KEY`      | مفتاح Geoapify للخرائط      | `********************************`                                                                                                                                                                                 |
| `SUPABASE_URL`          | رابط Supabase               | `https://geqnmbnhyzzhqcouldfz.supabase.co`                                                                                                                                                                         |
| `SUPABASE_ANON_KEY`     | مفتاح Supabase العام        | `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdlcW5tYm5oeXp6aHFjb3VsZGZ6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgxOTI3NTMsImV4cCI6MjA2Mzc2ODc1M30.TV92S0BtPGtihgoKjcsW2svZl74_EdcrtJ60AUnIaHw` |
| `GOOGLE_CLIENT_ID`      | معرف Google OAuth           | `79461320705-ru94lkf71prenrqpv9v9pnvnlvndcseb.apps.googleusercontent.com`                                                                                                                                          |

### متغيرات الفرونت إند (Frontend)

| اسم المتغير              | الوصف                  | القيمة الحالية                                                                                                                                                                                                     |
| ------------------------ | ---------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| `VITE_SUPABASE_URL`      | رابط Supabase          | `https://geqnmbnhyzzhqcouldfz.supabase.co`                                                                                                                                                                         |
| `VITE_SUPABASE_ANON_KEY` | مفتاح Supabase العام   | `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdlcW5tYm5oeXp6aHFjb3VsZGZ6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgxOTI3NTMsImV4cCI6MjA2Mzc2ODc1M30.TV92S0BtPGtihgoKjcsW2svZl74_EdcrtJ60AUnIaHw` |
| `VITE_GOOGLE_CLIENT_ID`  | معرف Google OAuth      | `79461320705-ru94lkf71prenrqpv9v9pnvnlvndcseb.apps.googleusercontent.com`                                                                                                                                          |
| `VITE_GEOAPIFY_API_KEY`  | مفتاح Geoapify للخرائط | `********************************`                                                                                                                                                                                 |

## 🚀 خطوات الإعداد في Render

### 1. تسجيل الدخول إلى Render

- اذهب إلى [Render Dashboard](https://dashboard.render.com)
- سجل الدخول بحسابك

### 2. اختيار الخدمة

- اختر خدمة الباك إند JobScope من قائمة الخدمات

### 3. إضافة متغيرات البيئة

- اضغط على **Settings** من القائمة العلوية
- مرر للأسفل إلى قسم **Environment Variables**
- اضغط على **Add Environment Variable**

### 4. إدخال المتغيرات

أدخل كل متغير على حدة:

```
Key: MONGODB_URI
Value: mongodb+srv://jobscope_user:<EMAIL>/jobscope?retryWrites=true&w=majority&appName=JobScope
```

```
Key: JWT_SECRET
Value: jobscope_secret_key_2024
```

```
Key: HYPERSENDER_API_TOKEN
Value: 250|e2Lq3UqTPIzYJBYhdJviP1Zb066RBHuOCWtkj5eY90306903
```

... وهكذا لجميع المتغيرات المذكورة أعلاه.

### 5. حفظ التغييرات

- بعد إدخال جميع المتغيرات، اضغط **Save Changes**
- سيتم إعادة تشغيل الخدمة تلقائياً

## ⚠️ ملاحظات مهمة

1. **لا ترفع ملف .env إلى GitHub** - تم إضافته إلى .gitignore
2. **استخدم القيم الحقيقية فقط في Render** - ليس في الكود
3. **تحقق من صحة القيم** قبل الحفظ
4. **احتفظ بنسخة احتياطية** من القيم في مكان آمن

## 🔍 التحقق من التطبيق

بعد إعداد المتغيرات، تحقق من:

- [ ] تشغيل الخادم بنجاح
- [ ] الاتصال بقاعدة البيانات
- [ ] عمل المصادقة
- [ ] إرسال الرسائل النصية
- [ ] رفع الصور

## 📞 الدعم

إذا واجهت مشاكل:

1. تحقق من سجلات Render (Logs)
2. تأكد من صحة جميع القيم
3. تحقق من انتهاء صلاحية المفاتيح
