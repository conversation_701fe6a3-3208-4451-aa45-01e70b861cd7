/*
 * تم استبدال Firebase بـ Supabase للمصادقة
 * Firebase محجوب في سوريا، Supabase بديل أفضل
 * هذا الملف محفوظ للتوافق مع الكود القديم
 */

// تكوين Firebase (محفوظ للتوافق)
import { initializeApp } from "firebase/app";
import { getAuth } from "firebase/auth";

// إعدادات Firebase (معطلة - تم استبدالها بـ Supabase)
const firebaseConfig = {
  apiKey:
    import.meta.env.VITE_FIREBASE_API_KEY ||
    "AIzaSyAr4xtIZgS6t_MHmhDwNDcgVtmZ5_ua-BI",
  authDomain:
    import.meta.env.VITE_FIREBASE_AUTH_DOMAIN ||
    "jobscope-6cfe6.firebaseapp.com",
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID || "jobscope-6cfe6",
  storageBucket:
    import.meta.env.VITE_FIREBASE_STORAGE_BUCKET ||
    "jobscope-6cfe6.firebasestorage.app",
  messagingSenderId:
    import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID || "329127698057",
  appId:
    import.meta.env.VITE_FIREBASE_APP_ID ||
    "1:329127698057:web:97f5d6144219c7bedbf058",
  measurementId: import.meta.env.VITE_FIREBASE_MEASUREMENT_ID || "G-MBQ5LW9TWP",
};

// تهيئة Firebase (معطلة)
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);

export { auth };
export default app;
