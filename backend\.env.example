# ملف مثال لمتغيرات البيئة
# انسخ هذا الملف إلى .env وأضف القيم الحقيقية
# لا تضع القيم الحقيقية في هذا الملف!

# إعدادات الخادم
PORT=5000
NODE_ENV=development

# قاعدة البيانات MongoDB
MONGODB_URI=mongodb+srv://username:<EMAIL>/database_name?retryWrites=true&w=majority&appName=AppName

# مفتاح JWT للمصادقة
JWT_SECRET=your_jwt_secret_key_here

# إعدادات البريد الإلكتروني
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-app-password
EMAIL_FROM=JobScope <<EMAIL>>

# إعدادات التشخيص
DEBUG=true
DEBUG_LEVEL=verbose

# إعدادات HyperSender SMS
HYPERSENDER_API_TOKEN=your_hypersender_api_token_here
HYPERSENDER_SENDER_ID=JobScope
HYPERSENDER_API_URL=https://api.hypersender.com/api/send

# إعدادات Imgbb لرفع الصور
IMGBB_API_KEY=your_imgbb_api_key_here

# إعدادات Geoapify للخرائط
GEOAPIFY_API_KEY=your_geoapify_api_key_here

# إعدادات Supabase
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your_supabase_anon_key_here

# إعدادات Google OAuth (إذا كنت تستخدمه)
GOOGLE_CLIENT_ID=your_google_client_id_here

# ملاحظات:
# 1. لا ترفع ملف .env إلى GitHub
# 2. استخدم متغيرات البيئة في Render لقيم الإنتاج
# 3. تأكد من إضافة .env إلى ملف .gitignore
